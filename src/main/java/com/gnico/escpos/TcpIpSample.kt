package com.gnico.escpos

import com.github.anastaciocintra.escpos.EscPos
import com.github.anastaciocintra.escpos.EscPosConst
import com.github.anastaciocintra.escpos.Style
import com.github.anastaciocintra.escpos.barcode.QRCode
import com.github.anastaciocintra.escpos.image.*
import com.github.anastaciocintra.output.TcpIpOutputStream
import java.awt.image.BufferedImage
import java.io.IOException
import java.util.logging.Level
import java.util.logging.Logger
import javax.imageio.ImageIO

class TcpIpSample {

    fun printInfo(host: String, port: Int) {
        try {
            TcpIpOutputStream(host, port).use { outputStream ->
                val escpos = EscPos(outputStream)

                val title = Style()
                    .setFontSize(Style.FontSize._2, Style.FontSize._2)
                    .setJustification(EscPosConst.Justification.Center)

                escpos.writeLF(title, "QR Code")
                escpos.feed(2)
                val qrcode = QRCode()

                escpos.writeLF("QRCode default options")
                escpos.feed(2)
                escpos.write(qrcode, "testaroni")
                escpos.feed(3)

                escpos.cut(EscPos.CutMode.PART)

                escpos.writeLF("QRCode size 6 and center justified")
                escpos.feed(2)
                qrcode.setSize(7)
                qrcode.setJustification(EscPosConst.Justification.Center)
                escpos.write(qrcode, "reddit.com")
                escpos.feed(3)
                escpos.cut(EscPos.CutMode.FULL)

                escpos.close()
            }
        } catch (ex: IOException) {
            Logger.getLogger(TcpIpSample::class.java.name).log(Level.SEVERE, null, ex)
        }
    }

    fun loadImage(name: String): BufferedImage? {
        var bufferedImage: BufferedImage? = null
        try {
            TcpIpSample::class.java.classLoader.getResourceAsStream(name)?.use { imageStream ->
                bufferedImage = ImageIO.read(imageStream)
                if (bufferedImage != null) {
                    println("Image loaded successfully!")
                } else {
                    println("Failed to load image.")
                }
            } ?: println("Image not found.")
        } catch (e: IOException) {
            println("Error loading image: ${e.message}")
        }
        return bufferedImage
    }

    fun printPicture(host: String, port: Int) {
        try {
            TcpIpOutputStream(host, port).use { outputStream ->
                val escpos = EscPos(outputStream)

                val title = Style()
                    .setFontSize(Style.FontSize._2, Style.FontSize._2)
                    .setJustification(EscPosConst.Justification.Center)

                escpos.writeLF(title, "Prueba de imagen")
                escpos.feed(2)
                val imageBufferedImage = loadImage("images/image.jpg")
                val imageWrapper = RasterBitImageWrapper()
                val algorithm: Bitonal = BitonalOrderedDither()
                val escposImage = EscPosImage(CoffeeImageImpl(imageBufferedImage), algorithm)
                escpos.write(imageWrapper, escposImage)
                escpos.feed(4)

                escpos.writeLF(title, "Prueba de impresion QR")
                escpos.feed(2)
                val qrcode = QRCode()
                qrcode.setSize(7)
                qrcode.setJustification(EscPosConst.Justification.Center)
                escpos.write(qrcode, "https://images.app.goo.gl/5EYjFcJKkwaXjVEh7")
                escpos.feed(4)

                escpos.cut(EscPos.CutMode.FULL)
                escpos.close()
            }
        } catch (ex: IOException) {
            Logger.getLogger(TcpIpSample::class.java.name).log(Level.SEVERE, null, ex)
        }
    }

    fun printCustom(host: String, port: Int) {
        try {
            TcpIpOutputStream(host, port).use { outputStream ->
                val escpos = EscPos(outputStream)

                val title = Style()
                    .setFontSize(Style.FontSize._3, Style.FontSize._3)
                    .setJustification(EscPosConst.Justification.Center)

                val subtitle = Style(escpos.style)
                    .setBold(true)
                    .setUnderline(Style.Underline.OneDotThick)
                
                val bold = Style(escpos.style)
                    .setBold(true)

                val size2 = Style()
                    .setFontSize(Style.FontSize._2, Style.FontSize._2)

                val size1 = Style()
                    .setFontSize(Style.FontSize._1, Style.FontSize._1)

                escpos.writeLF(title, "Semana")
                    .writeLF(title, "21/04/2025")
                    .feed(3)
                    .writeLF(size2, "Pagos:")
                    .feed(2)
                    .writeLF("HELADERAS                      $10264643")
                    .writeLF("EXPENSAS MERCADO                $2730704")
                    .writeLF("AQUARINE                         $359185")
                    .writeLF("CORREAS PABLO                     $55000")
                    .writeLF("CARPINTERO (BAULERA)             $600000")
                    .writeLF("BERARDI                          $425259")
                    .writeLF("LA MEJOR                         $328800")
                    .writeLF("DASDASUR                         $547170")
                    .writeLF("SARCOS (CONDIMENTOS)             $305800")
                    .writeLF("FIAMBRES (MANCUSO)               $490726")
                    .writeLF("AQUARINE                         $470727")
                    .writeLF("DARIO (ADELANTO)                 $200000")
                    .writeLF("VERDULERO                        $778227")
                    .writeLF("MOREIRA                          $760650")
                    .writeLF("-----------------------------------------")
                    .feed(2)
                    .writeLF(bold, "TOTAL                          $18316891")
                    .feed(8)
                    .cut(EscPos.CutMode.FULL)

                escpos.close()
            }
        } catch (ex: IOException) {
            Logger.getLogger(TcpIpSample::class.java.name).log(Level.SEVERE, null, ex)
        }
    }

    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            val obj = TcpIpSample()
            obj.printInfo("*************", 9100)

            // obj.printCustom("*************", 9100)

            /* PrintService printService = PrinterOutputStream.getPrintServiceByName(args[0]);
            PrinterOutputStream printerOutputStream = new PrinterOutputStream(printService);
            EscPos escpos = new EscPos(printerOutputStream);
            escpos.writeLF("Hello world");
            escpos.feed(5).cut(EscPos.CutMode.FULL);
            escpos.close(); */
        }
    }
}

fun main(args: Array<String>) {
    val obj = TcpIpSample()
    obj.printInfo("*************", 9100)

    // obj.printCustom("*************", 9100)
}
