package com.gnico.presentismo

import java.io.*
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

class PresentismoProcessor {

    data class RegistroPresentismo(
        val nombre: String,
        val numeroEmpleado: Int,
        val fecha: LocalDate,
        val hora: LocalTime
    )

    data class ResumenDiario(
        val fecha: LocalDate,
        val entrada: LocalTime? = null,
        val salida: LocalTime? = null,
        val estado: String // "NORMAL", "EXCEPCION", "AUSENCIA"
    )

    companion object {

        fun leerCSV(archivo: String): List<RegistroPresentismo> {
            val registros = mutableListOf<RegistroPresentismo>()
            val dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy")
            val timeFormatter = DateTimeFormatter.ofPattern("H:mm")

            BufferedReader(FileReader(archivo)).use { br ->
                br.readLine() // Saltar header

                var linea: String?
                while (br.readLine().also { linea = it } != null && linea!!.trim().isNotEmpty()) {
                    val campos = linea!!.split(",")
                    if (campos.size >= 5) {
                        val nombre = campos[0].trim()
                        val numeroEmpleado = campos[1].trim().toInt()
                        val fecha = LocalDate.parse(campos[3].trim(), dateFormatter)
                        val hora = LocalTime.parse(campos[4].trim(), timeFormatter)

                        registros.add(RegistroPresentismo(nombre, numeroEmpleado, fecha, hora))
                    }
                }
            }

            return registros
        }

        fun obtenerDiasLaborables(año: Int, mes: Int, excluirDomingos: Boolean = true): List<LocalDate> {
            val diasLaborables = mutableListOf<LocalDate>()
            val inicio = LocalDate.of(año, mes, 1)
            val fin = inicio.withDayOfMonth(inicio.lengthOfMonth())

            var fecha = inicio
            while (!fecha.isAfter(fin)) {
                if (!excluirDomingos || fecha.dayOfWeek != DayOfWeek.SUNDAY) {
                    diasLaborables.add(fecha)
                }
                fecha = fecha.plusDays(1)
            }

            return diasLaborables
        }

        fun generarReporte(
            registros: List<RegistroPresentismo>,
            empleados: Map<Int, String>,
            diasLaborables: List<LocalDate>,
            archivoSalida: String,
            tituloReporte: String
        ) {
            PrintWriter(FileWriter(archivoSalida)).use { writer ->
                writer.println("=".repeat(80))
                writer.println(tituloReporte)
                writer.println("=".repeat(80))
                writer.println()

                // Procesar cada empleado
                empleados.toSortedMap().forEach { (numeroEmpleado, nombre) ->
                    // Obtener registros del empleado agrupados por fecha
                    val registrosPorFecha = registros
                        .filter { it.numeroEmpleado == numeroEmpleado }
                        .groupBy { it.fecha }

                    // Escribir encabezado del empleado
                    escribirEncabezadoEmpleado(writer, numeroEmpleado, nombre)

                    // Procesar cada día laborable
                    diasLaborables.forEach { fecha ->
                        val registrosDia = registrosPorFecha[fecha]
                        val resumen = procesarDia(fecha, registrosDia)

                        // Escribir línea del reporte
                        escribirLineaReporteTabulado(writer, resumen, registrosDia?.size ?: 0)
                    }

                    // Línea separadora entre empleados
                    writer.println()
                    writer.println("-".repeat(80))
                    writer.println()
                }
            }

            println("Reporte generado exitosamente: $archivoSalida")
        }

        fun procesarDia(fecha: LocalDate, registrosDia: List<RegistroPresentismo>?): ResumenDiario {
            return when {
                registrosDia.isNullOrEmpty() -> ResumenDiario(fecha, estado = "AUSENCIA")
                registrosDia.size == 1 -> {
                    val hora = registrosDia[0].hora
                    ResumenDiario(fecha, entrada = hora, estado = "EXCEPCION")
                }
                else -> {
                    // Ordenar por hora para obtener entrada (más temprana) y salida (más tardía)
                    val horas = registrosDia.map { it.hora }.sorted()
                    val entrada = horas.first()
                    val salida = horas.last()
                    ResumenDiario(fecha, entrada = entrada, salida = salida, estado = "NORMAL")
                }
            }
        }

        fun escribirEncabezadoEmpleado(writer: PrintWriter, numeroEmpleado: Int, nombre: String) {
            writer.println("EMPLEADO: $nombre (ID: $numeroEmpleado)")
            writer.println()
            writer.println("Fecha      Día        Entrada  Salida   Horas Trab.  Registros  Estado")
            writer.println("-".repeat(70))
        }

        fun escribirLineaReporteTabulado(writer: PrintWriter, resumen: ResumenDiario, cantidadRegistros: Int) {
            val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
            val fechaStr = resumen.fecha.format(dateFormatter)
            val diaSemana = obtenerNombreDia(resumen.fecha.dayOfWeek).padEnd(10)

            val entrada = resumen.entrada?.toString() ?: "  --  "
            val salida = resumen.salida?.toString() ?: "  --  "
            val horasTrabajadas = calcularHorasTrabajadas(resumen.entrada, resumen.salida)

            val registrosStr = when (resumen.estado) {
                "AUSENCIA" -> "    0"
                "EXCEPCION" -> "    1"
                "NORMAL" -> String.format("%5d", cantidadRegistros)
                else -> "    ?"
            }

            val estadoStr = when (resumen.estado) {
                "AUSENCIA" -> "AUSENCIA"
                "EXCEPCION" -> "EXCEPCIÓN"
                "NORMAL" -> "NORMAL"
                else -> "DESCONOCIDO"
            }

            writer.printf(
                "%-10s %-10s %-8s %-8s %-11s %-10s %s%n",
                fechaStr, diaSemana, entrada, salida, horasTrabajadas, registrosStr, estadoStr
            )
        }

        fun calcularHorasTrabajadas(entrada: LocalTime?, salida: LocalTime?): String {
            return if (entrada != null && salida != null && !entrada.equals(salida)) {
                val duracion = Duration.between(entrada, salida)
                val horas = duracion.toHours()
                val minutos = duracion.toMinutes() % 60
                String.format("%02d:%02d", horas, minutos)
            } else {
                "  --:--  "
            }
        }

        fun obtenerNombreDia(dayOfWeek: DayOfWeek): String {
            return when (dayOfWeek) {
                DayOfWeek.MONDAY -> "Lunes"
                DayOfWeek.TUESDAY -> "Martes"
                DayOfWeek.WEDNESDAY -> "Miércoles"
                DayOfWeek.THURSDAY -> "Jueves"
                DayOfWeek.FRIDAY -> "Viernes"
                DayOfWeek.SATURDAY -> "Sábado"
                DayOfWeek.SUNDAY -> "Domingo"
            }
        }

        fun obtenerNombreMes(mes: Int): String {
            return when (mes) {
                1 -> "ENERO"
                2 -> "FEBRERO"
                3 -> "MARZO"
                4 -> "ABRIL"
                5 -> "MAYO"
                6 -> "JUNIO"
                7 -> "JULIO"
                8 -> "AGOSTO"
                9 -> "SEPTIEMBRE"
                10 -> "OCTUBRE"
                11 -> "NOVIEMBRE"
                12 -> "DICIEMBRE"
                else -> "MES_DESCONOCIDO"
            }
        }

        fun generarNombreArchivo(año: Int, mes: Int): String {
            val nombreMes = obtenerNombreMes(mes)
            return "src/main/resources/reports/ReportePresentismo${nombreMes}${año}.txt"
        }

        fun generarTituloReporte(año: Int, mes: Int): String {
            val nombreMes = obtenerNombreMes(mes)
            return "REPORTE DE PRESENTISMO - $nombreMes $año"
        }

        fun procesarMes(año: Int, mes: Int, archivoCSV: String? = null, excluirDomingos: Boolean = true) {
            try {
                // Generar nombre de archivo CSV si no se proporciona
                val archivoEntrada = archivoCSV ?: "src/main/resources/data/${obtenerNombreMes(mes)}${año}presentismo.csv"

                // Leer el archivo CSV
                val registros = leerCSV(archivoEntrada)

                // Obtener todos los días laborables del mes
                val diasLaborables = obtenerDiasLaborables(año, mes, excluirDomingos)

                // Procesar datos por empleado
                val empleados = registros.associate { it.numeroEmpleado to it.nombre }

                // Generar nombres automáticos
                val archivoSalida = generarNombreArchivo(año, mes)
                val titulo = generarTituloReporte(año, mes)

                // Generar reporte
                generarReporte(registros, empleados, diasLaborables, archivoSalida, titulo)

            } catch (e: Exception) {
                println("Error procesando el mes $mes/$año: ${e.message}")
                e.printStackTrace()
            }
        }
    }
}

fun main(args: Array<String>) {
    try {
        when (args.size) {
            0 -> {
                // Modo por defecto: Junio 2025
                println("Usando configuración por defecto: Junio 2025")
                PresentismoProcessor.procesarMes(2025, 6, "src/main/resources/data/JUNIOpresentismo.csv")
            }
            2 -> {
                // Modo: año mes
                val año = args[0].toInt()
                val mes = args[1].toInt()
                println("Procesando: ${PresentismoProcessor.obtenerNombreMes(mes)} $año")
                PresentismoProcessor.procesarMes(año, mes)
            }
            3 -> {
                // Modo: año mes archivo
                val año = args[0].toInt()
                val mes = args[1].toInt()
                val archivo = args[2]
                println("Procesando: ${PresentismoProcessor.obtenerNombreMes(mes)} $año con archivo: $archivo")
                PresentismoProcessor.procesarMes(año, mes, archivo)
            }
            else -> {
                println("Uso:")
                println("  mvn exec:java                           # Procesa Junio 2025 (por defecto)")
                println("  mvn exec:java -Dexec.args=\"2025 7\"      # Procesa Julio 2025")
                println("  mvn exec:java -Dexec.args=\"2025 8 archivo.csv\"  # Procesa Agosto 2025 con archivo específico")
                println()
                println("Parámetros:")
                println("  año: Año a procesar (ej: 2025)")
                println("  mes: Mes a procesar (1-12)")
                println("  archivo: Ruta del archivo CSV (opcional)")
            }
        }
    } catch (e: NumberFormatException) {
        println("Error: Los parámetros año y mes deben ser números válidos")
    } catch (e: Exception) {
        println("Error: ${e.message}")
        e.printStackTrace()
    }
}
