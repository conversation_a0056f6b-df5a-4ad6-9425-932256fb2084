# EscposTest

Proyecto Maven con Kotlin para procesamiento de datos de presentismo y manejo de impresoras ESC/POS.

## 📁 Estructura del Proyecto

```
EscposTest/
├── pom.xml                                    # Configuración Maven
├── README.md                                  # Este archivo
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── gnico/
│   │   │           ├── escpos/                # Módulo ESC/POS
│   │   │           │   ├── TcpIpSample.kt     # Ejemplo TCP/IP para impresoras (Kotlin)
│   │   │           │   └── Horarios.kt        # Utilidades de horarios (Kotlin)
│   │   │           └── presentismo/           # Módulo de presentismo
│   │   │               └── PresentismoProcessor.kt # Procesador de datos CSV (Kotlin)
│   │   └── resources/
│   │       ├── data/                          # Archivos de datos CSV
│   │       │   └── JUNIOpresentismo.csv       # Datos de presentismo de junio 2025
│   │       ├── images/                        # Imágenes y logos
│   │       │   ├── majologo.png
│   │       │   ├── alberto.jpg
│   │       │   ├── KristinaK.jpeg
│   │       │   └── image*.jpg
│   │       └── reports/                       # Reportes generados
│   │           ├── ReportePresentismoJunio2025.csv  # Formato CSV (legacy)
│   │           └── ReportePresentismoJunio2025.txt  # Formato tabulado mejorado
│   └── test/
│       └── java/                              # Tests unitarios
└── target/                                    # Archivos compilados (generado por Maven)
```

## 🛠️ Tecnologías

- **Kotlin 2.2.0** - Lenguaje principal
- **Java 17** - Runtime y compatibilidad
- **Maven** - Gestión de dependencias y build
- **ESC/POS Coffee 4.1.0** - Librería para impresoras ESC/POS

## 📋 Funcionalidades

### 1. Procesamiento de Presentismo
- **Clase:** `com.gnico.presentismo.PresentismoProcessor`
- **Función:** Procesa archivos CSV de registros de presentismo para cualquier mes/año
- **Características:**
  - **Completamente genérico** - funciona para cualquier mes y año
  - Agrupa registros por empleado y fecha
  - Calcula entrada (registro más temprano) y salida (registro más tardío)
  - Calcula horas trabajadas automáticamente
  - Identifica excepciones (un solo registro) y ausencias
  - Opción de excluir/incluir domingos
  - Genera reportes tabulados y organizados por empleado
  - Nombres de archivos automáticos basados en mes/año
  - Títulos de reportes dinámicos

### 2. Manejo de Impresoras ESC/POS
- **Clase:** `com.gnico.escpos.TcpIpSample`
- **Función:** Comunicación con impresoras ESC/POS vía TCP/IP
- **Características:**
  - Impresión de texto con diferentes estilos
  - Manejo de imágenes y logos
  - Códigos QR
  - Configuración de red TCP/IP

## 🚀 Uso

### Compilar el proyecto
```bash
mvn compile
```

### Ejecutar el procesador de presentismo

#### Modo por defecto (Junio 2025)
```bash
mvn exec:java
```

#### Procesar cualquier mes/año
```bash
# Procesar Julio 2025
mvn exec:java "-Dexec.args=2025 7"

# Procesar Agosto 2025 con archivo específico
mvn exec:java "-Dexec.args=2025 8 src/main/resources/data/agosto2025.csv"

# Ver ayuda
mvn exec:java "-Dexec.args=help"
```

### Ejecutar una clase específica
```bash
mvn exec:java -Dexec.mainClass="com.gnico.escpos.TcpIpSampleKt"
```

## 📊 Formato de Datos de Presentismo

### Archivo de entrada (CSV)
```csv
Name,Employee No.,Department,Date,Time,Device
Carlos Alberto Ledesma,14,Majo,06/29/2025,08:57,W1PRO
```

### Reporte generado (Formato Tabulado)
```
EMPLEADO: Carlos Alberto Ledesma (ID: 14)

Fecha      Día        Entrada  Salida   Horas Trab.  Registros  Estado
----------------------------------------------------------------------
29/06/2025 Domingo    08:57    16:48    07:51           2      NORMAL
30/06/2025 Lunes        --       --       --:--         0      AUSENCIA
```

### Estados posibles:
- **NORMAL:** Entrada y salida registradas correctamente (muestra horas trabajadas)
- **EXCEPCIÓN:** Solo un registro en el día (sin cálculo de horas)
- **AUSENCIA:** Sin registros en el día

### Columnas del reporte:
- **Fecha:** Fecha en formato DD/MM/YYYY
- **Día:** Día de la semana en español
- **Entrada:** Hora de entrada (registro más temprano)
- **Salida:** Hora de salida (registro más tardío)
- **Horas Trab.:** Horas trabajadas calculadas (HH:MM)
- **Registros:** Cantidad de registros del día
- **Estado:** NORMAL, EXCEPCIÓN o AUSENCIA

## 🔧 Flexibilidad y Configuración

### Procesamiento de Cualquier Mes/Año
El código es completamente genérico y puede procesar cualquier mes de cualquier año:

```bash
# Ejemplos de uso
mvn exec:java "-Dexec.args=2024 12"    # Diciembre 2024
mvn exec:java "-Dexec.args=2026 3"     # Marzo 2026
mvn exec:java "-Dexec.args=2025 1"     # Enero 2025
```

### Convenciones de Nombres de Archivos

#### Archivos de entrada automáticos:
- `ENERO2025presentismo.csv`
- `FEBRERO2025presentismo.csv`
- `MARZO2025presentismo.csv`
- etc.

#### Archivos de salida automáticos:
- `ReportePresentismoENERO2025.txt`
- `ReportePresentismoFEBRERO2025.txt`
- `ReportePresentismoMARZO2025.txt`
- etc.

### Opciones Avanzadas
- **Excluir domingos:** Por defecto excluye domingos, configurable
- **Archivo personalizado:** Especificar ruta de archivo CSV diferente
- **Manejo de errores:** Mensajes claros cuando faltan archivos

## 🔧 Configuración

### Dependencias principales
- `escpos-coffee:4.1.0` - Librería ESC/POS
- `kotlin-stdlib-jdk8` - Runtime de Kotlin
- `kotlin-test` - Testing para Kotlin

### Plugins Maven
- `kotlin-maven-plugin` - Compilación de Kotlin
- `maven-compiler-plugin` - Compilación de Java
- `exec-maven-plugin` - Ejecución de clases main

## 📝 Notas de Desarrollo

- El proyecto sigue la estructura estándar de Maven
- Los packages están organizados por funcionalidad
- Los resources están categorizados por tipo (data, images, reports)
- Desarrollado completamente en Kotlin 2.2.0
- Compatible con Java 17 runtime
- Configurado para compilación Kotlin con interoperabilidad Java

## 🤝 Contribución

Para contribuir al proyecto:
1. Mantener la estructura de packages establecida
2. Seguir las convenciones de naming de Kotlin
3. Usar características idiomáticas de Kotlin (data classes, extension functions, etc.)
4. Documentar nuevas funcionalidades
5. Agregar tests para nuevas características
